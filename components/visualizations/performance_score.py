import plotly.express as px
import pandas as pd
import streamlit as st
import numpy as np

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SUCCESS_RATE_THRESHOLD, SPEED_THRESHOLD

# Performance scoring constants
SUCCESS_RATE_THRESHOLD = 0.75
SPEED_THRESHOLD = 100
CONSISTENCY_THRESHOLD = 0.75

# Scoring weights
SUCCESS_WEIGHT = 0.5
SPEED_WEIGHT = 0.3
CONSISTENCY_WEIGHT = 0.2

# Hill function parameters
SUCCESS_HILL_POWER = 10
SPEED_HILL_POWER = 5
CONSISTENCY_HILL_POWER = 10
SUCCESS_SCALING_FACTOR = 1.055
CONSISTENCY_SCALING_FACTOR = 1.055


def _calculate_success_rate_by_llm(df):
    """
    Calculate the success rate for each LLM.

    Args:
        df (pd.DataFrame): Input dataframe with 'llm' and 'successful' columns

    Returns:
        pd.DataFrame: DataFrame with 'llm' and 'successful' columns
    """
    return df.groupby('llm')['successful'].mean().reset_index()


def _calculate_speed_by_llm(df):
    """
    Calculate the median characters per second for each LLM (only successful responses).

    Args:
        df (pd.DataFrame): Input dataframe with 'llm', 'successful', and 'characters_per_second' columns

    Returns:
        pd.DataFrame: DataFrame with 'llm' and 'characters_per_second' columns
    """
    successful_responses = df[df['successful'] == 1]
    return successful_responses.groupby('llm')['characters_per_second'].median().reset_index()


def _calculate_consistency_by_llm(df):
    """
    Calculate consistency metric for each LLM based on variation in success rates across test cases.

    The consistency is measured using a variation index that penalizes LLMs with inconsistent
    performance across different test cases.

    Args:
        df (pd.DataFrame): Input dataframe with 'test_case_id', 'llm', and 'successful' columns

    Returns:
        pd.DataFrame: DataFrame with 'llm' and 'variation_index' columns
    """
    # Get success rate for each test case and LLM combination
    test_case_success = df.groupby(['test_case_id', 'llm'])['successful'].mean().reset_index()

    # Calculate variation index: (2*(success_rate - 0.5))^2
    # This penalizes both very low and very high success rates, favoring consistency around 0.5
    test_case_success['variation_index'] = (2 * (test_case_success['successful'] - 0.5)) ** 2

    # Average variation index per LLM (lower values indicate more consistency)
    return test_case_success.groupby('llm')['variation_index'].mean().reset_index()


def _calculate_hill_function_score(values, threshold, power, scaling_factor=1.0):
    """
    Calculate scores using Hill function: (x^n / (x^n + threshold^n)) * scaling_factor

    Args:
        values (pd.Series): Input values to score
        threshold (float): Threshold value for the Hill function
        power (int): Power parameter for the Hill function
        scaling_factor (float): Scaling factor to apply to the result

    Returns:
        pd.Series: Calculated scores
    """
    return ((values ** power) / (values ** power + threshold ** power)) * scaling_factor


def _calculate_performance_metrics(df):
    """
    Calculate all performance metrics for each LLM.

    Args:
        df (pd.DataFrame): Input dataframe with LLM performance data

    Returns:
        pd.DataFrame: DataFrame with all calculated metrics and scores
    """
    # Calculate base metrics
    success_by_llm = _calculate_success_rate_by_llm(df)
    speed_by_llm = _calculate_speed_by_llm(df)
    consistency_by_llm = _calculate_consistency_by_llm(df)

    # Merge all metrics into a single dataframe
    performance_df = pd.merge(success_by_llm, speed_by_llm, on='llm')
    performance_df = pd.merge(performance_df, consistency_by_llm, on='llm')

    # Calculate Hill function scores for each metric
    performance_df['success_score'] = _calculate_hill_function_score(
        performance_df['successful'],
        SUCCESS_RATE_THRESHOLD,
        SUCCESS_HILL_POWER,
        SUCCESS_SCALING_FACTOR
    )

    performance_df['speed_score'] = _calculate_hill_function_score(
        performance_df['characters_per_second'],
        SPEED_THRESHOLD,
        SPEED_HILL_POWER
    )

    performance_df['consistency_score'] = _calculate_hill_function_score(
        performance_df['variation_index'],
        CONSISTENCY_THRESHOLD,
        CONSISTENCY_HILL_POWER,
        CONSISTENCY_SCALING_FACTOR
    )

    # Calculate weighted final score (scaled to 0-100)
    performance_df['weighted_score'] = (
        SUCCESS_WEIGHT * performance_df['success_score'] +
        SPEED_WEIGHT * performance_df['speed_score'] +
        CONSISTENCY_WEIGHT * performance_df['consistency_score']
    ) * 100

    return performance_df


@st.cache_data
def get_advanced_performance_score_chart(df):
    """
    Create a bar chart visualization showing weighted performance scores for each LLM.

    The performance score combines three metrics using Hill functions:
    - Success Rate (50% weight): Sigmoid function with 75% threshold
    - Speed (30% weight): Sigmoid function with 100 chars/sec threshold
    - Consistency (20% weight): Sigmoid function with 75% threshold

    Args:
        df (pd.DataFrame): Input dataframe with LLM performance data

    Returns:
        plotly.graph_objects.Figure: Interactive bar chart showing performance scores
    """
    performance_df = _calculate_performance_metrics(df)

    # Get LLMs in original order and color mapping
    llms = get_llms(df)
    color_mapping = get_llm_color_mapping()

    # Create interactive bar chart
    fig = px.bar(
        performance_df,
        x='llm',
        y='weighted_score',
        color='llm',
        color_discrete_map=color_mapping,
        title='Gewichteter Leistungswert (50% Erfolgsrate, 30% Geschwindigkeit, 20% Konsistenz)',
        labels={'weighted_score': 'Leistungswert', 'llm': 'LLM'},
        category_orders={'llm': llms}
    )

    # Add detailed hover information with component scores
    fig.update_traces(
        hovertemplate=(
            '<b>%{x}</b><br>'
            'Leistungswert: %{y:.1f}<br>'
            'Erfolgsrate: %{customdata[0]:.1f}%<br>'
            'Geschwindigkeit: %{customdata[1]:.1f} Z/s<br>'
            'Konsistenz: %{customdata[2]:.3f}'
        ),
        customdata=np.column_stack((
            performance_df['successful'] * 100,
            performance_df['characters_per_second'],
            performance_df['variation_index']
        ))
    )

    # Configure chart layout
    fig.update_xaxes(title=None)
    fig.update_yaxes(range=[0, 100])
    fig.update_layout(showlegend=False)

    return fig

@st.cache_data
def get_performance_score_table(df):
    """
    Create a formatted table showing detailed performance scores for each LLM.

    The table displays both normalized scores (0-100%) and raw metrics:
    - Success Rate Score (50% weight)
    - Speed Score (30% weight)
    - Consistency Score (20% weight)
    - Total Weighted Score
    - Raw success rate, speed, and consistency values

    Args:
        df (pd.DataFrame): Input dataframe with LLM performance data

    Returns:
        pd.DataFrame: Formatted table ready for display in Streamlit
    """
    # Calculate all performance metrics using the shared function
    performance_df = _calculate_performance_metrics(df)

    # Get LLMs in original order for consistent sorting
    llms = get_llms(df)

    # Create formatted table with percentage values
    table_df = pd.DataFrame({
        'LLM': performance_df['llm'],
        'Erfolgsrate (50%)': (performance_df['success_score'] * 100).round(1).astype(str) + '%',
        'Geschwindigkeit (30%)': (performance_df['speed_score'] * 100).round(1).astype(str) + '%',
        'Konsistenz (20%)': (performance_df['consistency_score'] * 100).round(1).astype(str) + '%',
        'Gesamtscore': performance_df['weighted_score'].round(1).astype(str) + '%',
        'Rohe Erfolgsrate': (performance_df['successful'] * 100).round(1).astype(str) + '%',
        'Zeichen/Sekunde': performance_df['characters_per_second'].round(1).astype(str),
        'Variationsindex': performance_df['variation_index'].round(3).astype(str)
    })

    # Sort table by original LLM order
    table_df['llm_order'] = table_df['LLM'].apply(
        lambda x: llms.index(x) if x in llms else 999
    )
    table_df = table_df.sort_values('llm_order').drop('llm_order', axis=1)

    return table_df
