"""
Streamlit session state management utilities.

This module provides utility functions for managing Streamlit session state
with type safety and convenient initialization patterns.
"""
from typing import Any, Optional, TypeVar, Callable

import streamlit as st

T = TypeVar('T')


def get_or_initialize_state(key: str, default_factory: Callable[[], T]) -> T:
    """Get a value from session state or initialize it if not present.

    Args:
        key: The key to use in session state
        default_factory: A function that creates the default value

    Returns:
        The value from session state or the newly created default
    """
    if key not in st.session_state:
        st.session_state[key] = default_factory()
    return st.session_state[key]


def set_state(key: str, value: Any) -> None:
    """Set a value in session state.

    Args:
        key: The key to use in session state
        value: The value to store
    """
    st.session_state[key] = value


def get_state(key: str, default: Optional[Any] = None) -> Any:
    """Get a value from session state with an optional default.

    Args:
        key: The key to use in session state
        default: The default value to return if key is not in session state

    Returns:
        The value from session state or the default
    """
    return st.session_state.get(key, default)
