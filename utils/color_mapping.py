"""
Color mapping utilities for LLM visualizations.

This module provides consistent color assignment for LLMs across all visualizations.
Colors are assigned once at application startup and maintained throughout the session
to ensure visual consistency.
"""
from typing import Dict, List

import streamlit as st
import plotly.express as px

# Color palette combining Set1 and D3 qualitative colors for visual variety
COLOR_PALETTE = px.colors.qualitative.Set1 + [px.colors.qualitative.D3[9]]

# Global storage for LLM color assignments
_GLOBAL_COLOR_MAPPING: Dict[str, str] = {}


def initialize_color_mapping(all_llms: List[str]) -> None:
    """
    Initialize the global color mapping with all available LLMs.

    This function should be called once when the application starts to ensure
    consistent color assignment across all visualizations. Each LLM gets a fixed
    color that remains constant regardless of filtering or selection.

    Args:
        all_llms: List of all LLM names available in the dataset
    """
    global _GLOBAL_COLOR_MAPPING

    # Assign a unique color to each LLM using modulo to cycle through palette
    for i, llm in enumerate(all_llms):
        color_index = i % len(COLOR_PALETTE)
        _GLOBAL_COLOR_MAPPING[llm] = COLOR_PALETTE[color_index]


@st.cache_data
def get_llm_color_mapping() -> Dict[str, str]:
    """
    Get the current LLM color mapping.

    Returns:
        Dictionary mapping LLM names to their assigned colors
    """
    return _GLOBAL_COLOR_MAPPING.copy()  # Return copy to prevent external modification
